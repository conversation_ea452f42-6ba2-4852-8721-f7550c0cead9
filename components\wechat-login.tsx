"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { X, QrCode } from "lucide-react"

interface WeChatLoginProps {
  isOpen: boolean
  onClose: () => void
}

export function WeChatLogin({ isOpen, onClose }: WeChatLoginProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="bg-white p-6 max-w-sm w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">微信登录</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="text-center">
          <div className="w-48 h-48 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
            <QrCode className="w-24 h-24 text-gray-400" />
          </div>
          <p className="text-sm text-gray-600 mb-4">请使用微信扫描二维码登录</p>
          <p className="text-xs text-gray-500">扫码后请在手机上确认登录</p>
        </div>
      </Card>
    </div>
  )
}
