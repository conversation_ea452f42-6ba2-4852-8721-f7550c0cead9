"use client"

import { X } from "lucide-react"
import { Button } from "@/components/ui/button"

interface ImageModalProps {
  isOpen: boolean
  onClose: () => void
  imageSrc: string
  title: string
}

export function ImageModal({ isOpen, onClose, imageSrc, title }: ImageModalProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="relative max-w-[90vw] max-h-[90vh] p-4">
        <Button
          variant="ghost"
          size="icon"
          className="absolute -top-2 -right-2 z-10 bg-white/10 hover:bg-white/20 text-white"
          onClick={onClose}
        >
          <X className="w-4 h-4" />
        </Button>

        <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
          <h3 className="text-white text-lg font-semibold mb-4 text-center">{title}</h3>
          <img
            src={imageSrc || "/placeholder.svg"}
            alt={title}
            className="max-w-full max-h-[70vh] object-contain rounded-lg mx-auto"
          />
        </div>
      </div>
    </div>
  )
}
