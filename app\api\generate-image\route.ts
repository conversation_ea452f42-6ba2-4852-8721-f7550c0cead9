import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { imageData } = await request.json()

    const fixedPrompt =
      "Turn this photo into a character figure. Behind it, place a box with the character's image printed on it, and a computer showing the Blender modeling process on its screen. In front of the box, add a round plastic base with the character figure standing on it. Set the scene indoors if possible."

    if (!imageData) {
      return NextResponse.json({ error: "Image data is required" }, { status: 400 })
    }

    console.log("[v0] Full environment debug:")
    console.log("[v0] NODE_ENV:", process.env.NODE_ENV)
    console.log("[v0] All environment variables:", Object.keys(process.env).sort())
    console.log("[v0] GEMINI_API_KEY exists:", !!process.env.GEMINI_API_KEY)
    console.log(
      "[v0] GEMINI_API_KEY value (first 10 chars):",
      process.env.GEMINI_API_KEY?.substring(0, 10) || "undefined",
    )

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        {
          error: "GEMINI_API_KEY 环境变量未配置",
          solution: "解决方案：",
          steps: [
            "1. 确认已在项目设置中添加 GEMINI_API_KEY",
            "2. 点击右上角 'Publish' 按钮重新部署项目",
            "3. 等待部署完成后再次尝试",
            "4. 如果仍有问题，请检查 API 密钥是否有效",
          ],
          debug: {
            nodeEnv: process.env.NODE_ENV,
            hasKey: !!process.env.GEMINI_API_KEY,
            allEnvKeys: Object.keys(process.env).length,
            geminiKeys: Object.keys(process.env).filter((key) => key.includes("GEMINI")),
          },
        },
        { status: 500 },
      )
    }

    // 根据官方文档使用新的 API
    const { GoogleGenAI } = await import("@google/genai")
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    })

    // 提取 base64 数据
    const base64Data = imageData.split(",")[1]

    // 根据官方文档格式构建 prompt
    const prompt = [
      { text: fixedPrompt },
      {
        inlineData: {
          mimeType: "image/png", // 支持多种格式
          data: base64Data,
        },
      },
    ]

    // 使用新的 API 调用方式
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash-image-preview",
      contents: prompt,
    })

    // 处理响应，支持文本和图片输出
    let resultText = ""
    let generatedImageData = null

    for (const part of response.candidates[0].content.parts) {
      if (part.text) {
        resultText += part.text
      } else if (part.inlineData) {
        // 如果返回了生成的图片
        generatedImageData = part.inlineData.data
      }
    }

    // 如果有生成的图片，返回 base64 数据
    const imageUrl = generatedImageData
      ? `data:image/png;base64,${generatedImageData}`
      : "/placeholder.svg?height=400&width=400&query=" + encodeURIComponent("character figure with box and computer setup")

    return NextResponse.json({
      success: true,
      result: resultText,
      imageUrl: imageUrl,
      hasGeneratedImage: !!generatedImageData,
    })
  } catch (error) {
    console.error("Error processing image:", error)
    return NextResponse.json(
      {
        error: "图片处理失败，请稍后重试",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
