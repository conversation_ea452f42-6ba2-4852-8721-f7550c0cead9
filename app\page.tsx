"use client"

import type React from "react"
import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Upload, Download, Sparkles, User } from "lucide-react"
import { WeChatLogin } from "@/components/wechat-login"
import { ImageModal } from "@/components/image-modal"

export default function ImageProcessor() {
  const [inputImage, setInputImage] = useState<string | null>(null)
  const [outputImage, setOutputImage] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showWeChatLogin, setShowWeChatLogin] = useState(false)
  const [showImageModal, setShowImageModal] = useState(false)
  const [modalImage, setModalImage] = useState<{ src: string; title: string } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setInputImage(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleProcess = async () => {
    if (!inputImage) return

    setIsProcessing(true)

    try {
      const response = await fetch("/api/generate-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageData: inputImage,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setOutputImage(data.imageUrl)
      } else {
        console.error("API Error:", data.error)
        setOutputImage(inputImage)
      }
    } catch (error) {
      console.error("Error processing image:", error)
      setOutputImage(inputImage)
    }

    setIsProcessing(false)
  }

  const handleDownload = () => {
    if (!outputImage) return

    const link = document.createElement("a")
    link.href = outputImage
    link.download = "processed-image.png"
    link.click()
  }

  const openImageModal = (src: string, title: string) => {
    setModalImage({ src, title })
    setShowImageModal(true)
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      <nav className="absolute top-0 left-0 right-0 z-20 p-4">
        <div className="flex justify-between items-center">
          <div className="text-white font-bold text-xl">{""}</div>
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10 bg-transparent border-white/20 border ml-auto"
            onClick={() => setShowWeChatLogin(true)}
          >
            <User className="w-4 h-4 mr-2" />
            登录
          </Button>
        </div>
      </nav>

      <div
        className="absolute inset-0"
        style={{
          backgroundImage: "url(/images/background-tile.png)",
          backgroundSize: "auto 450px",
          backgroundRepeat: "repeat",
        }}
      />

      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-2xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-4 bg-card/90 backdrop-blur-sm shadow-lg">
              <div className="text-center">
                <h2 className="text-lg font-bold text-card-foreground mb-3">Upload Image</h2>

                <div
                  className="border-2 border-dashed border-muted-foreground/30 rounded-lg p-4 mb-3 cursor-pointer hover:border-primary/50 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                >
                  {inputImage ? (
                    <img
                      src={inputImage || "/placeholder.svg"}
                      alt="Input"
                      className="max-w-full max-h-32 mx-auto rounded-lg object-contain cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation()
                        openImageModal(inputImage, "Input Image")
                      }}
                    />
                  ) : (
                    <div className="text-center">
                      <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">Click to upload or drag and drop</p>
                      <p className="text-xs text-muted-foreground mt-1">PNG, JPG, GIF up to 10MB</p>
                    </div>
                  )}
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />

                <Button onClick={() => fileInputRef.current?.click()} variant="outline" className="w-full mt-2">
                  <Upload className="w-4 h-4 mr-2" />
                  Choose File
                </Button>
              </div>
            </Card>

            <Card className="p-4 bg-card/90 backdrop-blur-sm shadow-lg">
              <div className="text-center">
                <h2 className="text-lg font-bold text-card-foreground mb-3">Processed Image</h2>

                <div className="border-2 border-dashed border-muted-foreground/30 rounded-lg p-4 mb-3 min-h-[120px] flex items-center justify-center">
                  {outputImage ? (
                    <img
                      src={outputImage || "/placeholder.svg"}
                      alt="Output"
                      className="max-w-full max-h-32 mx-auto rounded-lg object-contain cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => openImageModal(outputImage, "Processed Image")}
                    />
                  ) : (
                    <div className="text-center">
                      <Sparkles className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">Processed image will appear here</p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Button onClick={handleProcess} disabled={!inputImage || isProcessing} className="w-full">
                    {isProcessing ? (
                      <>
                        <div className="w-4 h-4 mr-2 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                        Generating with AI...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generate with AI
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={handleDownload}
                    disabled={!outputImage}
                    variant="outline"
                    className="w-full bg-transparent"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Result
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <WeChatLogin isOpen={showWeChatLogin} onClose={() => setShowWeChatLogin(false)} />

      {modalImage && (
        <ImageModal
          isOpen={showImageModal}
          onClose={() => setShowImageModal(false)}
          imageSrc={modalImage.src}
          title={modalImage.title}
        />
      )}
    </div>
  )
}
